@echo off
cls
echo ========================================
echo   FIXING VSCODE GIT ISSUE
echo   "too many active changes" ERROR
echo ========================================
echo.

echo Problem: 10,001 pending files in source control
echo VS Code Error: "too many active changes, only a subset of Git features will be enabled"
echo.

echo Step 1: Checking current git repository location...
git rev-parse --show-toplevel
echo.

echo Step 2: Removing ALL files from git tracking...
git rm -r --cached .

echo Step 3: Adding .gitignore first...
git add .gitignore

echo Step 4: Adding ONLY your project files...
git add DocAndSurveyManagement/Frontend/src/
echo ✓ Added Frontend source code (including Login.css)

git add DocAndSurveyManagement/Frontend/public/
echo ✓ Added Frontend public files

git add DocAndSurveyManagement/Frontend/package.json
echo ✓ Added package.json

if exist "DocAndSurveyManagement\Frontend\package-lock.json" (
    git add DocAndSurveyManagement/Frontend/package-lock.json
    echo ✓ Added package-lock.json
)

echo.
echo Step 5: Checking final status...
for /f %%i in ('git status --porcelain ^| find /c /v ""') do set count=%%i

echo ========================================
echo RESULT: %count% files (was 10,001)
echo ========================================

if %count% LSS 200 (
    echo ✅ SUCCESS! VS Code Git features will work now!
    echo.
    echo FINAL STEPS:
    echo 1. git commit -m "Fix VS Code git issue - remove excess files"
    echo 2. git push origin main
    echo.
    echo Your Login.css and all project files are safe!
) else (
    echo ⚠️ Still %count% files - may need additional cleanup
)

echo.
echo Restart VS Code after running the commit command.
pause
