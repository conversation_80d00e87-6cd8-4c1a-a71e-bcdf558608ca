const jwt = require('jsonwebtoken');
require('dotenv').config();

// Test JWT token creation and verification
const testToken = () => {
  const userId = '687bfb96076376710c9557d4';
  
  // Create token
  const token = jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: '7d' }
  );
  
  console.log('Generated token:', token);
  
  // Verify token
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    console.log('Decoded token:', decoded);
  } catch (error) {
    console.error('Token verification failed:', error);
  }
};

testToken();
