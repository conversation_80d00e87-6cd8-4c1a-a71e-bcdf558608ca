# Dependencies
node_modules/
**/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build outputs
build/
dist/
**/build/
**/dist/
.next/
out/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
Thumbs.db
desktop.ini
ehthumbs.db

# Logs and runtime data
logs/
*.log
pids/
*.pid
*.seed
*.pid.lock

# Coverage and test files
coverage/
.nyc_output/
.coverage
*.lcov

# Temporary files
*.tmp
*.temp
.cache/
.parcel-cache/

# Package manager files
.npm/
.yarn/
yarn-error.log
package-lock.json.bak

# TypeScript cache
*.tsbuildinfo

# Optional caches
.eslintcache
.stylelintcache

# Yarn Integrity file
.yarn-integrity

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
