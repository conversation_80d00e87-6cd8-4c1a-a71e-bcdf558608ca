/* Import colors and use same styling as Create<PERSON>urvey */
@import '../../styles/colors.css';

/* Edit Survey Hero */
.edit-survey-hero {
  min-height: 100vh;
  background: 
    linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #4fac<PERSON> 75%, #00f2fe 100%),
    radial-gradient(circle at 25% 75%, rgba(102, 126, 234, 0.3) 0%, transparent 60%),
    radial-gradient(circle at 75% 25%, rgba(240, 147, 251, 0.3) 0%, transparent 60%);
  background-size: 400% 400%, 150% 150%, 150% 150%;
  background-attachment: fixed;
  animation: editGradient<PERSON>low 30s ease infinite;
  padding: 2rem;
  position: relative;
}

.edit-survey-hero::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(248, 250, 252, 0.92);
  backdrop-filter: blur(25px) saturate(1.2);
  z-index: 0;
}

.edit-survey-hero > * {
  position: relative;
  z-index: 1;
}

/* Header Navigation */
.header-nav {
  margin-bottom: 1rem;
}

.back-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  display: inline-block;
}

.back-link:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateX(-2px);
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
}

/* Survey Header */
.survey-header {
  text-align: center;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.survey-title {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0 0 0.5rem 0;
}

.survey-subtitle {
  color: #64748b;
  font-size: 1.1rem;
  margin: 0;
}

/* Loading Container */
.loading-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: 
    linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #4facfe 75%, #00f2fe 100%);
  background-size: 400% 400%;
  animation: editGradientFlow 30s ease infinite;
}

.loading-spinner {
  font-size: 3rem;
  animation: spin 2s linear infinite;
  margin-bottom: 1rem;
}

.loading-container p {
  color: white;
  font-size: 1.2rem;
  font-weight: 500;
}

/* Form Glass Effect */
.survey-form-glass {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  max-width: 800px;
  margin: 0 auto;
}

/* Survey Basic Info */
.survey-basic-info {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(102, 126, 234, 0.2);
}

/* Form Groups */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 1);
}

.form-group input.error,
.form-group textarea.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error-text {
  color: #ef4444;
  font-size: 0.8rem;
  margin-top: 0.25rem;
  display: block;
}

/* Form Row */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Checkbox Label */
.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  margin-top: 1.5rem;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* Questions Section */
.questions-section {
  margin-bottom: 2rem;
}

.questions-section h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid rgba(102, 126, 234, 0.2);
}

/* Question Block */
.question-block {
  background: rgba(102, 126, 234, 0.05);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.question-header h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.remove-question-btn {
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.remove-question-btn:hover {
  background: #fecaca;
  transform: translateY(-1px);
}

/* Options Section */
.options-section {
  margin-top: 1rem;
}

.option-input {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  align-items: center;
}

.option-input input {
  flex: 1;
}

.remove-option-btn {
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.7rem;
  transition: all 0.3s ease;
}

.remove-option-btn:hover {
  background: #fecaca;
}

.add-option-btn {
  background: #f0f9ff;
  color: #0369a1;
  border: 1px solid #bae6fd;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.8rem;
  margin-top: 0.5rem;
  transition: all 0.3s ease;
}

.add-option-btn:hover {
  background: #e0f2fe;
  transform: translateY(-1px);
}

/* Rating Preview */
.rating-preview {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
}

.rating-stars {
  display: flex;
  gap: 0.25rem;
  margin-top: 0.5rem;
}

.star {
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.star:hover {
  transform: scale(1.1);
}

/* Add Question Button */
.add-question-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: block;
  margin: 0 auto;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.add-question-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.4);
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  padding-top: 2rem;
  border-top: 1px solid rgba(102, 126, 234, 0.2);
}

.draft-btn,
.submit-btn,
.publish-btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.draft-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.draft-btn:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
}

.submit-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.4);
}

.publish-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.publish-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(16, 185, 129, 0.4);
}

.draft-btn:disabled,
.submit-btn:disabled,
.publish-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Success/Error Messages */
.success-message {
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
  color: #166534;
  padding: 1rem;
  border-radius: 12px;
  text-align: center;
  margin-bottom: 2rem;
  font-weight: 500;
  border: 1px solid #86efac;
}

.error-message {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  color: #dc2626;
  padding: 1rem;
  border-radius: 12px;
  text-align: center;
  margin-bottom: 2rem;
  font-weight: 500;
  border: 1px solid #fca5a5;
}

/* Animations */
@keyframes editGradientFlow {
  0%, 100% { background-position: 0% 50%, 0% 0%, 100% 100%; }
  50% { background-position: 100% 50%, 50% 50%, 50% 50%; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .edit-survey-hero {
    padding: 1rem;
  }
  
  .survey-form-glass {
    padding: 1.5rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .draft-btn,
  .submit-btn,
  .publish-btn {
    width: 100%;
    max-width: 300px;
  }
}
