import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import { surveyAPI, responseAPI } from "../../services/api";
import ErrorMessage from "../../components/ErrorMessage";
import "./AdminDashboard.css";

const AdminDashboard = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const [surveys, setSurveys] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [stats, setStats] = useState({
    totalSurveys: 0,
    publishedSurveys: 0,
    totalResponses: 0,
    draftSurveys: 0
  });

  // Load surveys and calculate stats
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        setError('');

        const result = await surveyAPI.getMy();

        if (result.success) {
          const surveyData = result.data.surveys;
          setSurveys(surveyData);

          // Calculate stats
          const totalSurveys = surveyData.length;
          const publishedSurveys = surveyData.filter(s => s.status === 'published').length;
          const draftSurveys = surveyData.filter(s => s.status === 'draft').length;
          const totalResponses = surveyData.reduce((sum, s) => sum + (s.totalResponses || 0), 0);

          setStats({
            totalSurveys,
            publishedSurveys,
            draftSurveys,
            totalResponses
          });
        } else {
          setError(result.message);
        }
      } catch (error) {
        setError('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  const handleLogout = () => {
    logout();
  };

  const handleDeleteSurvey = async (surveyId) => {
    if (window.confirm('Are you sure you want to delete this survey? This action cannot be undone.')) {
      try {
        const result = await surveyAPI.delete(surveyId);
        if (result.success) {
          setSurveys(surveys.filter(s => s._id !== surveyId));
          // Recalculate stats
          const updatedSurveys = surveys.filter(s => s._id !== surveyId);
          const totalSurveys = updatedSurveys.length;
          const publishedSurveys = updatedSurveys.filter(s => s.status === 'published').length;
          const draftSurveys = updatedSurveys.filter(s => s.status === 'draft').length;
          const totalResponses = updatedSurveys.reduce((sum, s) => sum + (s.totalResponses || 0), 0);

          setStats({
            totalSurveys,
            publishedSurveys,
            draftSurveys,
            totalResponses
          });
        } else {
          setError(result.message);
        }
      } catch (error) {
        setError('Failed to delete survey');
      }
    }
  };

  const handlePublishSurvey = async (surveyId) => {
    try {
      const result = await surveyAPI.publish(surveyId);
      if (result.success) {
        // Update the survey in the list
        setSurveys(surveys.map(s =>
          s._id === surveyId
            ? { ...s, status: 'published', publishedAt: new Date().toISOString() }
            : s
        ));
        // Recalculate stats
        const updatedSurveys = surveys.map(s =>
          s._id === surveyId ? { ...s, status: 'published' } : s
        );
        const publishedSurveys = updatedSurveys.filter(s => s.status === 'published').length;
        const draftSurveys = updatedSurveys.filter(s => s.status === 'draft').length;

        setStats(prev => ({
          ...prev,
          publishedSurveys,
          draftSurveys
        }));
      } else {
        setError(result.message);
      }
    } catch (error) {
      setError('Failed to publish survey');
    }
  };

  return (
    <div className="admin-dashboard">
      {/* Header */}
      <header className="dashboard-header">
        <div className="header-content">
          <div className="header-left">
            <h1>🔧 Admin Dashboard</h1>
            <p>Welcome back, {user?.name}!</p>
          </div>
          <div className="header-right">
            <span className="user-info">
              <span className="user-role">Admin</span>
              <span className="user-name">{user?.name}</span>
            </span>
            <button onClick={handleLogout} className="logout-btn">
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="dashboard-main">
        <div className="dashboard-container">

          <ErrorMessage
            message={error}
            onClose={() => setError('')}
          />

          {/* Dashboard Stats */}
          <div className="dashboard-stats">
            <div className="stat-card">
              <div className="stat-icon">📊</div>
              <div className="stat-content">
                <h3>{stats.totalSurveys}</h3>
                <p>Total Surveys</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">🚀</div>
              <div className="stat-content">
                <h3>{stats.publishedSurveys}</h3>
                <p>Published</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">📝</div>
              <div className="stat-content">
                <h3>{stats.draftSurveys}</h3>
                <p>Drafts</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">👥</div>
              <div className="stat-content">
                <h3>{stats.totalResponses}</h3>
                <p>Total Responses</p>
              </div>
            </div>
          </div>

          {/* Core Functionalities */}
          <div className="core-functions">
            <h2>Survey Management</h2>
            <div className="function-cards">
              <Link to="/create-survey" className="function-card create">
                <div className="function-icon">📝</div>
                <div className="function-content">
                  <h3>Quick Survey Creator</h3>
                  <p>Create simple surveys in just a few clicks</p>
                </div>
                <div className="function-arrow">→</div>
              </Link>

              <Link to="/survey-list" className="function-card update">
                <div className="function-icon">✏️</div>
                <div className="function-content">
                  <h3>Update Survey</h3>
                  <p>Edit existing surveys, modify questions and settings</p>
                </div>
                <div className="function-arrow">→</div>
              </Link>

              <Link to="/survey-list" className="function-card delete">
                <div className="function-icon">🗑️</div>
                <div className="function-content">
                  <h3>Delete Survey</h3>
                  <p>Remove surveys that are no longer needed</p>
                </div>
                <div className="function-arrow">→</div>
              </Link>

              <Link to="/survey-list" className="function-card view">
                <div className="function-icon">👁️</div>
                <div className="function-content">
                  <h3>View Survey</h3>
                  <p>Browse and preview all created surveys</p>
                </div>
                <div className="function-arrow">→</div>
              </Link>

            </div>
          </div>

          {/* Recent Surveys */}
          <div className="recent-surveys">
            <div className="section-header">
              <h2>Recent Surveys</h2>
              <Link to="/survey-list" className="view-all-btn">View All</Link>
            </div>

            {loading ? (
              <div className="loading-state">
                <div className="loading-spinner"></div>
                <p>Loading surveys...</p>
              </div>
            ) : surveys.length === 0 ? (
              <div className="empty-state">
                <div className="empty-icon">📋</div>
                <h3>No Surveys Yet</h3>
                <p>Create your first survey to get started!</p>
                <Link to="/create-survey" className="create-first-btn">
                  Create Survey
                </Link>
              </div>
            ) : (
              <div className="surveys-grid">
                {surveys.slice(0, 3).map(survey => (
                  <div key={survey._id} className="survey-card">
                    <div className="survey-header">
                      <h3>{survey.title}</h3>
                      <span className={`survey-status ${survey.status}`}>
                        {survey.status === 'published' ? '🚀 Published' : '📝 Draft'}
                      </span>
                    </div>
                    <p className="survey-description">{survey.description}</p>
                    <div className="survey-meta">
                      <span className="question-count">
                        📝 {survey.questions?.length || 0} questions
                      </span>
                      <span className="response-count">
                        👥 {survey.totalResponses || 0} responses
                      </span>
                      <span className="survey-date">
                        📅 {new Date(survey.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="survey-actions">
                      {survey.status === 'draft' && (
                        <button
                          className="publish-btn"
                          onClick={() => handlePublishSurvey(survey._id)}
                        >
                          🚀 Publish
                        </button>
                      )}
                      <Link
                        to={`/survey-responses/${survey._id}`}
                        className="view-responses-btn"
                      >
                        📊 Responses
                      </Link>
                      <button
                        className="delete-btn"
                        onClick={() => handleDeleteSurvey(survey._id)}
                      >
                        🗑️ Delete
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

        </div>
      </main>
    </div>
  );
};

export default AdminDashboard;
