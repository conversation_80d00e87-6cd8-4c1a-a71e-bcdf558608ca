@echo off
cls
echo ========================================
echo   CLEANING SOURCE CONTROL
echo   Keeping ONLY project files
echo ========================================
echo.

echo Removing ALL files from git tracking...
git rm -r --cached .

echo Adding .gitignore first...
git add .gitignore

echo Adding ONLY project files and folders...

REM Add the main project folder
git add DocAndSurveyManagement/

REM Add specific project files if they exist at root
if exist "README.md" git add README.md
if exist "package.json" git add package.json

echo.
echo Checking final file count...
for /f %%i in ('git status --porcelain ^| find /c /v ""') do set count=%%i

echo ========================================
echo RESULT: %count% files (was 10,000+)
echo ========================================

if %count% LSS 500 (
    echo ✅ SUCCESS! Source control cleaned!
    echo.
    echo FINAL STEPS:
    echo git commit -m "Clean source control - keep only project files"
    echo git push origin main
) else (
    echo ⚠️ Still %count% files - check what's included
    echo Run: git status
)

echo.
echo Your Login.css and all project files are safe!
pause
