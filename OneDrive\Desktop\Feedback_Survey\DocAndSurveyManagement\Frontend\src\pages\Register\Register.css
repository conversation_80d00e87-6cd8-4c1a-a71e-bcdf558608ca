/* Import colors */
@import '../../styles/colors.css';

/* Register container */
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  padding: 1rem;
}

.register-container.user-theme {
  background: linear-gradient(135deg, #dbeafe 0%, #f8fafc 100%);
}

.register-container.admin-theme {
  background: linear-gradient(135deg, #ede9fe 0%, #f8fafc 100%);
}

/* Back button */
.back-button {
  text-align: left;
  margin-bottom: 1rem;
}

.back-link {
  color: #64748b;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.back-link:hover {
  color: #3b82f6;
}

/* Register icon */
.register-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.user-icon {
  color: #3b82f6;
}

.admin-icon {
  color: #8b5cf6;
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%, 0% 0%, 100% 100%, 50% 50%;
  }
  20% {
    background-position: 30% 20%, 10% 30%, 90% 70%, 40% 60%;
  }
  40% {
    background-position: 70% 80%, 30% 60%, 70% 40%, 80% 20%;
  }
  60% {
    background-position: 100% 50%, 50% 90%, 50% 10%, 20% 80%;
  }
  80% {
    background-position: 60% 10%, 80% 20%, 20% 80%, 60% 40%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.7;
  }
  20% {
    transform: translateY(-25px) rotate(1.5deg) scale(1.03);
    opacity: 0.9;
  }
  40% {
    transform: translateY(15px) rotate(-0.5deg) scale(0.97);
    opacity: 1;
  }
  60% {
    transform: translateY(-35px) rotate(2deg) scale(1.06);
    opacity: 0.8;
  }
  80% {
    transform: translateY(25px) rotate(-1.5deg) scale(0.94);
    opacity: 0.95;
  }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(-360deg); }
}

/* Floating Elements */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.floating-element {
  position: absolute;
  font-size: 2.5rem;
  opacity: 0.5;
  animation: floatDance 18s ease-in-out infinite;
  filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.15));
}

.element-1 {
  top: 8%;
  left: 12%;
  animation-delay: 0s;
  animation-duration: 20s;
}

.element-2 {
  top: 15%;
  right: 10%;
  animation-delay: -3s;
  animation-duration: 25s;
}

.element-3 {
  bottom: 25%;
  left: 6%;
  animation-delay: -7s;
  animation-duration: 22s;
}

.element-4 {
  bottom: 12%;
  right: 15%;
  animation-delay: -12s;
  animation-duration: 27s;
}

.element-5 {
  top: 45%;
  left: 3%;
  animation-delay: -18s;
  animation-duration: 24s;
}

.element-6 {
  top: 65%;
  right: 5%;
  animation-delay: -6s;
  animation-duration: 26s;
}

.element-7 {
  top: 30%;
  left: 50%;
  animation-delay: -14s;
  animation-duration: 23s;
}

.element-8 {
  bottom: 40%;
  right: 50%;
  animation-delay: -9s;
  animation-duration: 21s;
}

@keyframes floatDance {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
    opacity: 0.3;
  }
  20% {
    transform: translateY(-40px) translateX(30px) rotate(72deg) scale(1.2);
    opacity: 0.6;
  }
  40% {
    transform: translateY(25px) translateX(-20px) rotate(144deg) scale(0.8);
    opacity: 0.8;
  }
  60% {
    transform: translateY(-20px) translateX(35px) rotate(216deg) scale(1.1);
    opacity: 0.7;
  }
  80% {
    transform: translateY(30px) translateX(-25px) rotate(288deg) scale(0.9);
    opacity: 0.5;
  }
}

/* Register card */
.register-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.register-card::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(135deg,
    rgba(79, 172, 254, 0.4) 0%,
    rgba(0, 242, 254, 0.4) 20%,
    rgba(102, 126, 234, 0.4) 40%,
    rgba(118, 75, 162, 0.4) 60%,
    rgba(240, 147, 251, 0.4) 80%,
    rgba(79, 172, 254, 0.4) 100%);
  border-radius: 35px;
  z-index: -2;
  animation: registerBorderFlow 8s ease infinite;
}

.register-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(60deg, transparent 20%, rgba(255, 255, 255, 0.12) 40%, transparent 60%),
    radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.08) 0%, transparent 40%);
  border-radius: 32px;
  z-index: -1;
  animation: registerShimmer 10s ease infinite;
}

@keyframes registerGlow {
  0%, 100% {
    box-shadow:
      0 50px 100px rgba(0, 0, 0, 0.18),
      0 25px 50px rgba(79, 172, 254, 0.12),
      0 10px 20px rgba(0, 242, 254, 0.12),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    box-shadow:
      0 60px 120px rgba(0, 0, 0, 0.25),
      0 30px 60px rgba(79, 172, 254, 0.2),
      0 15px 30px rgba(0, 242, 254, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 1),
      inset 0 -1px 0 rgba(255, 255, 255, 0.4);
  }
}

@keyframes registerBorderFlow {
  0%, 100% {
    background: linear-gradient(135deg,
      rgba(79, 172, 254, 0.4) 0%,
      rgba(0, 242, 254, 0.4) 20%,
      rgba(102, 126, 234, 0.4) 40%,
      rgba(118, 75, 162, 0.4) 60%,
      rgba(240, 147, 251, 0.4) 80%,
      rgba(79, 172, 254, 0.4) 100%);
  }
  33% {
    background: linear-gradient(135deg,
      rgba(240, 147, 251, 0.5) 0%,
      rgba(79, 172, 254, 0.5) 20%,
      rgba(0, 242, 254, 0.5) 40%,
      rgba(102, 126, 234, 0.5) 60%,
      rgba(118, 75, 162, 0.5) 80%,
      rgba(240, 147, 251, 0.5) 100%);
  }
  66% {
    background: linear-gradient(135deg,
      rgba(0, 242, 254, 0.4) 0%,
      rgba(240, 147, 251, 0.4) 20%,
      rgba(79, 172, 254, 0.4) 40%,
      rgba(118, 75, 162, 0.4) 60%,
      rgba(102, 126, 234, 0.4) 80%,
      rgba(0, 242, 254, 0.4) 100%);
  }
}

@keyframes registerShimmer {
  0%, 100% {
    background-position: 0% 0%, 0% 0%;
    opacity: 0.5;
  }
  50% {
    background-position: 100% 100%, 100% 100%;
    opacity: 0.9;
  }
}

/* Header */
.register-header {
  text-align: center;
  margin-bottom: 2rem;
}

.register-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 0.5rem;
}

.register-header p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

/* Form styles */
.register-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.form-control {
  padding: 0.875rem 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #fff;
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control:hover {
  border-color: #c1c9d2;
}

/* Register button */
.register-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 10px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 0.5rem;
}

.register-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.register-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Error message */
.error-message {
  background: #fee;
  color: #c53030;
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid #fed7d7;
  font-size: 0.9rem;
  text-align: center;
}

/* Login link */
.login-link {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e1e5e9;
}

.login-link p {
  color: #666;
  margin: 0;
}

.login-link a {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.login-link a:hover {
  color: #764ba2;
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .register-card {
    padding: 2rem 1.5rem;
    margin: 1rem;
  }
  
  .register-header h1 {
    font-size: 2rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-control {
    padding: 0.75rem;
  }
}

/* Button styles for user/admin themes */
.register-btn.user-btn {
  background: #3b82f6;
  color: white;
}

.register-btn.user-btn:hover:not(:disabled) {
  background: #2563eb;
}

.register-btn.admin-btn {
  background: #8b5cf6;
  color: white;
}

.register-btn.admin-btn:hover:not(:disabled) {
  background: #7c3aed;
}
