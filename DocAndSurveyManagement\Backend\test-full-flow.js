const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testFullFlow() {
  try {
    console.log('🔐 Testing login...');
    
    // Login as admin
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'admin123'
    });
    
    console.log('✅ Login successful');
    const adminToken = loginResponse.data.data.token;
    console.log('Token:', adminToken.substring(0, 50) + '...');
    
    // Create a survey
    console.log('\n📝 Creating survey...');
    const surveyData = {
      title: 'Test Survey from API',
      description: 'This is a test survey created via API',
      status: 'published',
      questions: [
        {
          id: 'q1',
          question: 'How do you rate our service?',
          type: 'rating',
          required: true
        },
        {
          id: 'q2',
          question: 'What can we improve?',
          type: 'text',
          required: false
        }
      ]
    };
    
    const surveyResponse = await axios.post(`${BASE_URL}/surveys`, surveyData, {
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Survey created successfully');
    const surveyId = surveyResponse.data.data.survey._id;
    console.log('Survey ID:', surveyId);
    
    // Login as user
    console.log('\n👤 Logging in as user...');
    const userLoginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      username: 'user',
      password: 'user123'
    });
    
    const userToken = userLoginResponse.data.data.token;
    console.log('✅ User login successful');
    
    // Submit response
    console.log('\n📋 Submitting response...');
    const responseData = {
      surveyId: surveyId,
      answers: [
        {
          questionId: 'q1',
          answer: '4'
        },
        {
          questionId: 'q2',
          answer: 'Everything looks great!'
        }
      ]
    };
    
    const submitResponse = await axios.post(`${BASE_URL}/responses`, responseData, {
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Response submitted successfully');
    console.log('Response ID:', submitResponse.data.data.response.id);
    
    // Get survey responses (as admin)
    console.log('\n📊 Getting survey responses...');
    const responsesResponse = await axios.get(`${BASE_URL}/responses/survey/${surveyId}`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });
    
    console.log('✅ Retrieved responses successfully');
    console.log('Total responses:', responsesResponse.data.data.count);
    
    console.log('\n🎉 All tests passed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testFullFlow();
