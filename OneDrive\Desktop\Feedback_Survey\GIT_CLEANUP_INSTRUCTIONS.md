# 🧹 Git Repository Cleanup Instructions

## Problem: 10k+ files in git (too many to push)

This happens because `node_modules`, build files, and other unnecessary files are being tracked by git.

## 🚀 Quick Solution (Choose ONE method):

### Method 1: Use the Cleanup Script (Recommended)
1. Open PowerShell as Administrator
2. Navigate to your project: `cd "OneDrive\Desktop\FeedbackAndSurveyManagement"`
3. Run: `.\cleanup-repo.ps1`
4. Follow the on-screen instructions

### Method 2: Manual Commands
Open Command Prompt or PowerShell in your project folder and run these commands:

```bash
# 1. Check current status
git status

# 2. Remove all files from tracking (keeps files locally)
git rm -r --cached .

# 3. Add .gitignore first
git add .gitignore

# 4. Add only essential files
git add DocAndSurveyManagement/Frontend/src/
git add DocAndSurveyManagement/Frontend/public/
git add DocAndSurveyManagement/Frontend/package.json
git add DocAndSurveyManagement/Frontend/package-lock.json

# Add backend if it exists
git add DocAndSurveyManagement/Backend/

# Add root files
git add README.md

# 5. Check what will be committed (should be < 200 files)
git status

# 6. Commit the cleaned repository
git commit -m "Clean repository - remove node_modules and build artifacts"

# 7. Push to GitHub
git push origin main
```

## 📁 What Files Should Be Included:

✅ **INCLUDE these files:**
- `DocAndSurveyManagement/Frontend/src/` (all source code)
- `DocAndSurveyManagement/Frontend/public/` (public assets)
- `DocAndSurveyManagement/Frontend/package.json`
- `DocAndSurveyManagement/Frontend/package-lock.json`
- `DocAndSurveyManagement/Backend/` (if exists)
- `README.md`
- `.gitignore`

❌ **EXCLUDE these files:**
- `node_modules/` (dependencies - can be reinstalled)
- `build/` or `dist/` (build outputs)
- `.env` files (environment variables)
- `logs/` (log files)
- `.vscode/` or `.idea/` (IDE settings)
- `Thumbs.db`, `.DS_Store` (OS files)

## 🔍 Verify Success:

After cleanup, you should see:
- **File count**: Less than 200 files
- **Repository size**: Much smaller
- **No node_modules**: Not tracked by git

Check with: `git status` and `git ls-files | wc -l`

## 🆘 If Still Having Issues:

### Complete Reset (Nuclear Option):
```bash
# Delete .git folder and start fresh
rmdir /s .git
git init
git add .gitignore
git commit -m "Add .gitignore"
git add DocAndSurveyManagement/Frontend/src/
git add DocAndSurveyManagement/Frontend/public/
git add DocAndSurveyManagement/Frontend/package.json
git commit -m "Initial commit - clean project"
git remote add origin https://github.com/yourusername/your-repo.git
git push -u origin main
```

## 📝 Expected Final Result:

Your repository should contain approximately:
- 50-150 source files (.js, .jsx, .css, .html)
- Package configuration files
- Documentation files
- **NO** node_modules (thousands of files)
- **NO** build artifacts

## 🎯 Next Steps After Cleanup:

1. **Verify**: `git status` shows clean state
2. **Commit**: `git commit -m "Clean repository"`
3. **Push**: `git push origin main`
4. **Clone test**: Clone the repo elsewhere to verify it works
5. **Install deps**: `npm install` in the cloned repo

## 💡 Prevention for Future:

Always ensure `.gitignore` is the FIRST file you add to any new repository:
```bash
git init
git add .gitignore
git commit -m "Add .gitignore"
# Then add other files
```

---

**Need Help?** 
- Check file count: `git ls-files | wc -l`
- See tracked files: `git ls-files`
- Repository size: `git count-objects -vH`
