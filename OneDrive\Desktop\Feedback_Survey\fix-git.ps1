Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    FIXING 10K+ FILES ISSUE NOW" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Step 1: Removing ALL files from git tracking..." -ForegroundColor Yellow
git rm -r --cached .

Write-Host "Step 2: Adding .gitignore to prevent future issues..." -ForegroundColor Yellow
git add .gitignore

Write-Host "Step 3: Adding ONLY essential project files..." -ForegroundColor Yellow
git add "DocAndSurveyManagement/Frontend/src/"
Write-Host "✓ Added Frontend/src/" -ForegroundColor Green

git add "DocAndSurveyManagement/Frontend/public/"
Write-Host "✓ Added Frontend/public/" -ForegroundColor Green

git add "DocAndSurveyManagement/Frontend/package.json"
Write-Host "✓ Added package.json" -ForegroundColor Green

if (Test-Path "DocAndSurveyManagement/Frontend/package-lock.json") {
    git add "DocAndSurveyManagement/Frontend/package-lock.json"
    Write-Host "✓ Added package-lock.json" -ForegroundColor Green
}

Write-Host ""
Write-Host "Step 4: Checking file count..." -ForegroundColor Yellow
$fileCount = (git status --porcelain).Count
Write-Host "Files to commit: $fileCount" -ForegroundColor White

if ($fileCount -lt 200) {
    Write-Host ""
    Write-Host "✅ SUCCESS! Repository cleaned from 10k+ to $fileCount files" -ForegroundColor Green
    Write-Host ""
    Write-Host "FINAL STEPS:" -ForegroundColor Cyan
    Write-Host "1. git commit -m 'Clean repository - remove node_modules and unnecessary files'" -ForegroundColor White
    Write-Host "2. git push origin main" -ForegroundColor White
} else {
    Write-Host ""
    Write-Host "⚠️ Still $fileCount files - may need manual cleanup" -ForegroundColor Yellow
    Write-Host "Run: git status" -ForegroundColor White
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "         CLEANUP COMPLETE" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
