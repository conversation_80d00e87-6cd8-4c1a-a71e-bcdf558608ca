// API configuration and service functions
const API_BASE_URL = 'http://localhost:5000/api';

// Helper function to get auth token from localStorage
const getAuthToken = () => {
  const user = localStorage.getItem('user');
  if (user) {
    const userData = JSON.parse(user);
    return userData.token;
  }
  return null;
};

// Helper function to make API requests
const apiRequest = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  const token = getAuthToken();
  
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }
    
    return data;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

// Auth API functions
export const authAPI = {
  // Register new user
  register: async (userData) => {
    try {
      const response = await apiRequest('/auth/register', {
        method: 'POST',
        body: JSON.stringify(userData),
      });
      
      // Store user data and token in localStorage
      if (response.success && response.data) {
        const userWithToken = {
          ...response.data.user,
          token: response.data.token
        };
        localStorage.setItem('user', JSON.stringify(userWithToken));
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Registration failed'
      };
    }
  },

  // Login user
  login: async (username, password, role) => {
    try {
      const response = await apiRequest('/auth/login', {
        method: 'POST',
        body: JSON.stringify({ username, password, role }),
      });
      
      // Store user data and token in localStorage
      if (response.success && response.data) {
        const userWithToken = {
          ...response.data.user,
          token: response.data.token
        };
        localStorage.setItem('user', JSON.stringify(userWithToken));
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Login failed'
      };
    }
  },

  // Get current user
  getCurrentUser: async () => {
    try {
      const response = await apiRequest('/auth/me');
      return response;
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Failed to get user data'
      };
    }
  },

  // Logout user
  logout: async () => {
    try {
      const response = await apiRequest('/auth/logout', {
        method: 'POST',
      });
      
      // Remove user data from localStorage
      localStorage.removeItem('user');
      
      return response;
    } catch (error) {
      // Even if API call fails, remove local data
      localStorage.removeItem('user');
      return {
        success: true,
        message: 'Logged out successfully'
      };
    }
  }
};

// Health check function
export const healthCheck = async () => {
  try {
    const response = await apiRequest('/health');
    return response;
  } catch (error) {
    return {
      success: false,
      message: 'Backend server is not responding'
    };
  }
};

// Export default API object
const api = {
  auth: authAPI,
  healthCheck
};

export default api;
