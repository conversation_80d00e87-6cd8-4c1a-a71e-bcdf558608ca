// API configuration and service functions
const API_BASE_URL = 'http://localhost:5000/api';

// Test API connection
console.log('API Base URL:', API_BASE_URL);

// Helper function to get auth token from localStorage
const getAuthToken = () => {
  const user = localStorage.getItem('user');
  if (user) {
    try {
      const userData = JSON.parse(user);
      return userData.token;
    } catch (error) {
      console.error('Error parsing user data:', error);
      localStorage.removeItem('user');
      return null;
    }
  }
  return null;
};

// Helper function to make API requests
const apiRequest = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  const token = getAuthToken();
  
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      // Handle specific HTTP status codes
      if (response.status === 401) {
        // Unauthorized - clear local storage and redirect to login
        localStorage.removeItem('user');
        window.location.href = '/login';
      }

      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    // Handle network errors
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error('Unable to connect to server. Please check your internet connection.');
    }

    console.error('API request failed:', error);
    throw error;
  }
};

// Auth API functions
export const authAPI = {
  // Register new user
  register: async (userData) => {
    try {
      // Validate required fields on frontend
      const requiredFields = ['username', 'email', 'password', 'name'];
      const missingFields = requiredFields.filter(field => !userData[field]);

      if (missingFields.length > 0) {
        return {
          success: false,
          message: `Missing required fields: ${missingFields.join(', ')}`
        };
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(userData.email)) {
        return {
          success: false,
          message: 'Please enter a valid email address'
        };
      }

      // Validate password length
      if (userData.password.length < 6) {
        return {
          success: false,
          message: 'Password must be at least 6 characters long'
        };
      }

      const response = await apiRequest('/auth/register', {
        method: 'POST',
        body: JSON.stringify(userData),
      });

      // Store user data and token in localStorage
      if (response.success && response.data) {
        const userWithToken = {
          ...response.data.user,
          token: response.data.token
        };
        localStorage.setItem('user', JSON.stringify(userWithToken));
      }

      return response;
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Registration failed. Please try again.'
      };
    }
  },

  // Login user
  login: async (username, password, role) => {
    try {
      console.log('Login attempt:', { username, role });

      // Validate input
      if (!username || !password) {
        return {
          success: false,
          message: 'Username and password are required'
        };
      }

      if (username.length < 3) {
        return {
          success: false,
          message: 'Username must be at least 3 characters long'
        };
      }

      const response = await apiRequest('/auth/login', {
        method: 'POST',
        body: JSON.stringify({ username, password, role }),
      });

      console.log('Login response:', response);

      // Store user data and token in localStorage
      if (response.success && response.data) {
        const userWithToken = {
          ...response.data.user,
          token: response.data.token
        };
        localStorage.setItem('user', JSON.stringify(userWithToken));
        console.log('User data stored in localStorage');
      }

      return response;
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        message: error.message || 'Login failed. Please check your credentials.'
      };
    }
  },

  // Get current user
  getCurrentUser: async () => {
    try {
      const response = await apiRequest('/auth/me');
      return response;
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Failed to get user data'
      };
    }
  },

  // Logout user
  logout: async () => {
    try {
      const response = await apiRequest('/auth/logout', {
        method: 'POST',
      });
      
      // Remove user data from localStorage
      localStorage.removeItem('user');
      
      return response;
    } catch (error) {
      // Even if API call fails, remove local data
      localStorage.removeItem('user');
      return {
        success: true,
        message: 'Logged out successfully'
      };
    }
  }
};

// Health check function
export const healthCheck = async () => {
  try {
    console.log('Testing API connection to:', API_BASE_URL + '/health');
    const response = await apiRequest('/health');
    console.log('Health check response:', response);
    return response;
  } catch (error) {
    console.error('Health check failed:', error);
    return {
      success: false,
      message: 'Backend server is not responding'
    };
  }
};

// Export default API object
const api = {
  auth: authAPI,
  healthCheck
};

export default api;
