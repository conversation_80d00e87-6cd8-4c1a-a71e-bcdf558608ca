const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI)
  .then(() => console.log('✅ Connected to MongoDB'))
  .catch(err => console.error('❌ MongoDB connection error:', err));

// User schema (simplified)
const userSchema = new mongoose.Schema({
  username: { type: String, required: true, unique: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  name: { type: String, required: true },
  role: { type: String, enum: ['user', 'admin'], default: 'user' },
  isActive: { type: Boolean, default: true },
  lastLogin: Date,
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const User = mongoose.model('User', userSchema);

async function createDefaultUsers() {
  try {
    // Clear existing users
    await User.deleteMany({});
    console.log('🗑️ Cleared existing users');

    // Create admin user
    const adminPassword = await bcrypt.hash('admin123', 12);
    const admin = new User({
      username: 'admin',
      email: '<EMAIL>',
      password: adminPassword,
      name: 'Administrator',
      role: 'admin'
    });
    await admin.save();
    console.log('✅ Created admin user (admin/admin123)');

    // Create regular user
    const userPassword = await bcrypt.hash('user123', 12);
    const user = new User({
      username: 'user',
      email: '<EMAIL>',
      password: userPassword,
      name: 'Regular User',
      role: 'user'
    });
    await user.save();
    console.log('✅ Created regular user (user/user123)');

    // Create test user
    const testPassword = await bcrypt.hash('test123', 12);
    const testUser = new User({
      username: 'testuser',
      email: '<EMAIL>',
      password: testPassword,
      name: 'Test User',
      role: 'user'
    });
    await testUser.save();
    console.log('✅ Created test user (testuser/test123)');

    console.log('\n🎉 Default users created successfully!');
    console.log('You can now login with:');
    console.log('- Admin: admin/admin123');
    console.log('- User: user/user123');
    console.log('- Test: testuser/test123');

  } catch (error) {
    console.error('❌ Error creating users:', error);
  } finally {
    mongoose.connection.close();
  }
}

createDefaultUsers();
