@echo off
cls
echo ========================================
echo   REMOVING 10,001 PENDING CHANGES
echo ========================================
echo.

echo Current pending changes: 10,001
echo Target: Keep only project files (under 100)
echo.

echo Step 1: Unstaging ALL files from source control...
git reset HEAD .

echo Step 2: Removing ALL files from git tracking...
git rm -r --cached .

echo Step 3: Adding .gitignore to prevent future issues...
git add .gitignore

echo Step 4: Adding ONLY essential project files...
echo Adding Frontend source code...
git add DocAndSurveyManagement/Frontend/src/

echo Adding Frontend public files...
git add DocAndSurveyManagement/Frontend/public/

echo Adding package configuration...
git add DocAndSurveyManagement/Frontend/package.json

if exist "DocAndSurveyManagement\Frontend\package-lock.json" (
    git add DocAndSurveyManagement/Frontend/package-lock.json
    echo Added package-lock.json
)

echo.
echo Step 5: Checking final file count...
for /f %%i in ('git status --porcelain ^| find /c /v ""') do set final_count=%%i
echo.
echo ========================================
echo RESULT: %final_count% files (was 10,001)
echo ========================================
echo.

if %final_count% LSS 150 (
    echo ✅ SUCCESS! Source control cleaned!
    echo.
    echo FINAL COMMANDS TO RUN:
    echo git commit -m "Remove 10k+ files - keep project files only"
    echo git push origin main
    echo.
    echo Your project files are safe and ready to push!
) else (
    echo ⚠️ Still %final_count% files - check git status
)

echo.
echo Files now in source control:
git ls-files | head -20
echo.
pause
