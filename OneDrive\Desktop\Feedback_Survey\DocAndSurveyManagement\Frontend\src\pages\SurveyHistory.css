/* Survey History Container */
.survey-history-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 25%, #93c5fd 50%, #60a5fa 75%, #3b82f6 100%);
  padding: 2rem 1rem;
  position: relative;
}

.survey-history-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  z-index: 1;
}

.survey-history-container > * {
  position: relative;
  z-index: 2;
}

/* Header */
.history-header {
  text-align: center;
  margin-bottom: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.back-btn {
  background: none;
  border: none;
  color: #64748b;
  font-size: 0.9rem;
  cursor: pointer;
  margin-bottom: 1rem;
  transition: color 0.2s ease;
}

.back-btn:hover {
  color: #3b82f6;
}

.history-header h1 {
  font-size: 2.5rem;
  font-weight: 800;
  color: #000000;
  margin: 0 0 1rem 0;
}

.history-header p {
  color: #000000;
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.8;
}

/* Content */
.history-content {
  max-width: 1200px;
  margin: 0 auto;
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: 4rem 2rem;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(59, 130, 246, 0.3);
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  color: #000000;
  font-size: 1.1rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #000000;
  margin: 0 0 1rem 0;
}

.empty-state p {
  color: #000000;
  margin: 0 0 2rem 0;
  opacity: 0.8;
}

.provide-feedback-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.875rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.provide-feedback-btn:hover {
  background: #2563eb;
}

/* History Grid */
.history-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.history-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.history-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.card-category {
  background: #f3f4f6;
  color: #374151;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-submitted {
  background: #fef3c7;
  color: #92400e;
}

.status-progress {
  background: #dbeafe;
  color: #1e40af;
}

.status-reviewed {
  background: #d1fae5;
  color: #065f46;
}

.status-completed {
  background: #dcfce7;
  color: #166534;
}

.card-subject {
  font-size: 1.25rem;
  font-weight: 700;
  color: #000000;
  margin: 0 0 1rem 0;
}

.card-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.rating-stars {
  color: #fbbf24;
  font-size: 1.1rem;
}

.rating-text {
  color: #000000;
  font-size: 0.9rem;
  opacity: 0.7;
}

.card-message {
  color: #000000;
  line-height: 1.6;
  margin: 0 0 1rem 0;
  opacity: 0.8;
}

.card-suggestions {
  background: #f9fafb;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #000000;
}

.card-suggestions strong {
  color: #374151;
}

.card-footer {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
  margin-top: 1rem;
}

.submission-date {
  color: #000000;
  font-size: 0.9rem;
  opacity: 0.7;
}

/* History Actions */
.history-actions {
  text-align: center;
  margin-top: 3rem;
}

.new-feedback-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.new-feedback-btn:hover {
  background: #059669;
}

/* Responsive Design */
@media (max-width: 768px) {
  .survey-history-container {
    padding: 1rem;
  }
  
  .history-header h1 {
    font-size: 2rem;
  }
  
  .history-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .history-card {
    padding: 1.5rem;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .history-header h1 {
    font-size: 1.75rem;
  }
  
  .history-card {
    padding: 1.25rem;
  }
  
  .card-subject {
    font-size: 1.1rem;
  }
}
