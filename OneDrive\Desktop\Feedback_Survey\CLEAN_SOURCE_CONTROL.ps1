Clear-Host
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   CLEANING SOURCE CONTROL" -ForegroundColor Cyan
Write-Host "   Keeping ONLY project files" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Removing ALL files from git tracking..." -ForegroundColor Yellow
git rm -r --cached .

Write-Host "Adding .gitignore first..." -ForegroundColor Yellow
git add .gitignore

Write-Host "Adding ONLY project files and folders..." -ForegroundColor Yellow

# Add the main project folder
git add "DocAndSurveyManagement/"
Write-Host "✓ Added DocAndSurveyManagement/ (includes your Login.css)" -ForegroundColor Green

# Add specific project files if they exist at root
if (Test-Path "README.md") {
    git add "README.md"
    Write-Host "✓ Added README.md" -ForegroundColor Green
}

if (Test-Path "package.json") {
    git add "package.json"
    Write-Host "✓ Added package.json" -ForegroundColor Green
}

Write-Host ""
Write-Host "Checking final file count..." -ForegroundColor Yellow
$count = (git status --porcelain).Count

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "RESULT: $count files (was 10,000+)" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

if ($count -lt 500) {
    Write-Host ""
    Write-Host "✅ SUCCESS! Source control cleaned!" -ForegroundColor Green
    Write-Host ""
    Write-Host "FINAL STEPS:" -ForegroundColor Cyan
    Write-Host "git commit -m 'Clean source control - keep only project files'" -ForegroundColor White
    Write-Host "git push origin main" -ForegroundColor White
} else {
    Write-Host ""
    Write-Host "⚠️ Still $count files - check what's included" -ForegroundColor Yellow
    Write-Host "Run: git status" -ForegroundColor White
}

Write-Host ""
Write-Host "Your Login.css and all project files are safe!" -ForegroundColor Green
Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
