.error-message {
  margin: 1rem 0;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: 1px solid;
  animation: slideIn 0.3s ease-out;
}

.error-message.error {
  background-color: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.error-message.success {
  background-color: #f0fdf4;
  border-color: #bbf7d0;
  color: #16a34a;
}

.error-message.warning {
  background-color: #fffbeb;
  border-color: #fed7aa;
  color: #d97706;
}

.error-message.info {
  background-color: #eff6ff;
  border-color: #bfdbfe;
  color: #2563eb;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.error-icon {
  font-size: 1.1rem;
  flex-shrink: 0;
}

.error-text {
  flex: 1;
  font-weight: 500;
  font-size: 0.9rem;
}

.error-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.error-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
