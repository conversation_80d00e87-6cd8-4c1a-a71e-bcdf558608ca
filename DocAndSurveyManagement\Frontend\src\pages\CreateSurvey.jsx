import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useSurvey } from "../contexts/SurveyContext";
import { surveyAPI } from "../services/api";
import ErrorMessage from "../components/ErrorMessage";
import Navbar from "../Components/Navbar";
import "./CreateSurvey.css";

const CreateSurvey = () => {
  const navigate = useNavigate();
  const { createSurvey, loading } = useSurvey();

  const [surveyData, setSurveyData] = useState({
    title: "",
    description: "",
    status: "draft",
    questions: [{
      id: Date.now().toString(),
      question: "",
      type: "text",
      required: false,
      options: []
    }],
  });

  const [errors, setErrors] = useState({});
  const [success, setSuccess] = useState("");
  const [loading, setLoading] = useState(false);

  const questionTypes = [
    { value: "text", label: "📝 Text Answer" },
    { value: "multiple-choice", label: "☑️ Multiple Choice" },
    { value: "rating", label: "⭐ Rating (1-5)" },
    { value: "yes-no", label: "✔️ Yes/No" }
  ];

  const validateForm = () => {
    const newErrors = {};

    if (!surveyData.title.trim()) {
      newErrors.title = "Survey title is required";
    }

    if (!surveyData.description.trim()) {
      newErrors.description = "Survey description is required";
    }

    surveyData.questions.forEach((q, index) => {
      if (!q.question.trim()) {
        newErrors[`question_${index}`] = "Question text is required";
      }

      if ((q.type === 'multiple-choice' || q.type === 'checkbox') && q.options.length < 2) {
        newErrors[`options_${index}`] = "At least 2 options are required";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSurveyData({ ...surveyData, [name]: value });
    if (errors[name]) {
      setErrors({ ...errors, [name]: "" });
    }
  };

  const handleQuestionChange = (index, field, value) => {
    const updatedQuestions = [...surveyData.questions];
    updatedQuestions[index][field] = value;

    if (field === 'type') {
      if (value === 'multiple-choice' || value === 'checkbox') {
        updatedQuestions[index].options = ['Option 1', 'Option 2'];
      } else if (value === 'rating') {
        updatedQuestions[index].options = ['1', '2', '3', '4', '5'];
      } else if (value === 'yes-no') {
        updatedQuestions[index].options = ['Yes', 'No'];
      } else {
        updatedQuestions[index].options = [];
      }
    }

    setSurveyData({ ...surveyData, questions: updatedQuestions });

    if (errors[`question_${index}`] || errors[`options_${index}`]) {
      const newErrors = { ...errors };
      delete newErrors[`question_${index}`];
      delete newErrors[`options_${index}`];
      setErrors(newErrors);
    }
  };

  const addQuestion = () => {
    setSurveyData({
      ...surveyData,
      questions: [...surveyData.questions, {
        id: Date.now().toString(),
        question: "",
        type: "text",
        required: false,
        options: []
      }],
    });
  };

  const removeQuestion = (index) => {
    if (surveyData.questions.length > 1) {
      const updatedQuestions = surveyData.questions.filter((_, i) => i !== index);
      setSurveyData({ ...surveyData, questions: updatedQuestions });
    }
  };

  const addOption = (questionIndex) => {
    const updatedQuestions = [...surveyData.questions];
    const optionNumber = updatedQuestions[questionIndex].options.length + 1;
    updatedQuestions[questionIndex].options.push(`Option ${optionNumber}`);
    setSurveyData({ ...surveyData, questions: updatedQuestions });
  };

  const updateOption = (questionIndex, optionIndex, value) => {
    const updatedQuestions = [...surveyData.questions];
    updatedQuestions[questionIndex].options[optionIndex] = value;
    setSurveyData({ ...surveyData, questions: updatedQuestions });
  };

  const removeOption = (questionIndex, optionIndex) => {
    const updatedQuestions = [...surveyData.questions];
    if (updatedQuestions[questionIndex].options.length > 2) {
      updatedQuestions[questionIndex].options.splice(optionIndex, 1);
      setSurveyData({ ...surveyData, questions: updatedQuestions });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setErrors({});

    try {
      const result = await surveyAPI.create(surveyData);

      if (result.success) {
        setSuccess("Survey created successfully!");
        setTimeout(() => {
          navigate('/admin-dashboard');
        }, 2000);
      } else {
        setErrors({ submit: result.message });
      }
    } catch (error) {
      setErrors({ submit: 'Failed to create survey. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  const saveDraft = async () => {
    setLoading(true);
    setErrors({});

    try {
      const draftData = { ...surveyData, status: 'draft' };
      const result = await surveyAPI.create(draftData);

      if (result.success) {
        setSuccess("Survey saved as draft!");
        setTimeout(() => {
          navigate('/admin-dashboard');
        }, 2000);
      } else {
        setErrors({ submit: result.message });
      }
    } catch (error) {
      setErrors({ submit: 'Failed to save draft. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  const publishSurvey = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setErrors({});

    try {
      const publishData = { ...surveyData, status: 'published' };
      const result = await surveyAPI.create(publishData);

      if (result.success) {
        setSuccess("Survey published successfully!");
        setTimeout(() => {
          navigate('/admin-dashboard');
        }, 2000);
      } else {
        setErrors({ submit: result.message });
      }
    } catch (error) {
      setErrors({ submit: 'Failed to publish survey. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Navbar />
      <div className="survey-hero">
        <div className="survey-header">
          <h2 className="survey-title">📝 Quick Survey Creator</h2>
          <p className="survey-subtitle">Create simple surveys in just a few steps</p>
        </div>

        {success && (
          <ErrorMessage
            message={success}
            type="success"
            onClose={() => setSuccess('')}
          />
        )}

        <ErrorMessage
          message={errors.submit}
          onClose={() => setErrors({})}
        />

        <form className="survey-form-glass" onSubmit={handleSubmit}>
          {/* Survey Basic Info */}
          <div className="survey-basic-info">
            <div className="form-group">
              <label htmlFor="title">Survey Title *</label>
              <input
                type="text"
                id="title"
                name="title"
                value={surveyData.title}
                onChange={handleInputChange}
                placeholder="Enter survey title"
                className={errors.title ? 'error' : ''}
              />
              {errors.title && <span className="error-text">{errors.title}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="description">Survey Description *</label>
              <textarea
                id="description"
                name="description"
                value={surveyData.description}
                onChange={handleInputChange}
                placeholder="Describe what this survey is about"
                rows="3"
                className={errors.description ? 'error' : ''}
              />
              {errors.description && <span className="error-text">{errors.description}</span>}
            </div>
          </div>

          {/* Questions Section */}
          <div className="questions-section">
            <h3>📋 Add Your Questions</h3>
            <p className="questions-help">Start with one question and add more as needed</p>

            {surveyData.questions.map((question, index) => (
              <div key={question.id} className="question-block">
                <div className="question-header">
                  <h4>Question {index + 1}</h4>
                  {surveyData.questions.length > 1 && (
                    <button
                      type="button"
                      className="remove-question-btn"
                      onClick={() => removeQuestion(index)}
                      title="Remove this question"
                    >
                      ❌
                    </button>
                  )}
                </div>

                <div className="form-group">
                  <label>Question Text *</label>
                  <input
                    type="text"
                    value={question.question}
                    onChange={(e) => handleQuestionChange(index, 'question', e.target.value)}
                    placeholder="Enter your question"
                    className={errors[`question_${index}`] ? 'error' : ''}
                  />
                  {errors[`question_${index}`] && (
                    <span className="error-text">{errors[`question_${index}`]}</span>
                  )}
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Question Type</label>
                    <select
                      value={question.type}
                      onChange={(e) => handleQuestionChange(index, 'type', e.target.value)}
                      className="question-type-select"
                    >
                      {questionTypes.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group required-toggle">
                    <label className="checkbox-label">
                      <input
                        type="checkbox"
                        checked={question.required}
                        onChange={(e) => handleQuestionChange(index, 'required', e.target.checked)}
                      />
                      <span className="checkmark"></span>
                      Required
                    </label>
                  </div>
                </div>

                {/* Options for multiple choice questions */}
                {question.type === 'multiple-choice' && (
                  <div className="options-section">
                    <label>Answer Choices</label>
                    {question.options.map((option, optionIndex) => (
                      <div key={optionIndex} className="option-input">
                        <span className="option-number">{optionIndex + 1}.</span>
                        <input
                          type="text"
                          value={option}
                          onChange={(e) => updateOption(index, optionIndex, e.target.value)}
                          placeholder={`Choice ${optionIndex + 1}`}
                        />
                        {question.options.length > 2 && (
                          <button
                            type="button"
                            className="remove-option-btn"
                            onClick={() => removeOption(index, optionIndex)}
                            title="Remove this choice"
                          >
                            ❌
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      type="button"
                      className="add-option-btn"
                      onClick={() => addOption(index)}
                    >
                      ➕ Add Choice
                    </button>
                    {errors[`options_${index}`] && (
                      <span className="error-text">{errors[`options_${index}`]}</span>
                    )}
                  </div>
                )}

                {/* Preview for rating questions */}
                {question.type === 'rating' && (
                  <div className="rating-preview">
                    <label>Rating Preview:</label>
                    <div className="rating-stars">
                      {[1, 2, 3, 4, 5].map(star => (
                        <span key={star} className="star">⭐</span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}

            <button type="button" className="add-question-btn" onClick={addQuestion}>
              ➕ Add Question
            </button>
          </div>

          {/* Action Buttons */}
          <div className="form-actions">
            <button
              type="button"
              className="draft-btn"
              onClick={saveDraft}
              disabled={loading}
            >
              {loading ? '⏳ Saving...' : '💾 Save Draft'}
            </button>

            <button
              type="button"
              className="publish-btn"
              onClick={publishSurvey}
              disabled={loading}
            >
              {loading ? '⏳ Publishing...' : '🚀 Create & Publish'}
            </button>
          </div>
        </form>
      </div>
    </>
  );
};

export default CreateSurvey;
