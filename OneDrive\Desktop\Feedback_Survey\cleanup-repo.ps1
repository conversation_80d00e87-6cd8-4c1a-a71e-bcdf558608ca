# Git Repository Cleanup Script
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Git Repository Cleanup Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check current status
Write-Host "Step 1: Checking current git status..." -ForegroundColor Yellow
$currentFiles = (git status --porcelain).Count
Write-Host "Current files to be committed: $currentFiles" -ForegroundColor White
Write-Host ""

if ($currentFiles -gt 1000) {
    Write-Host "WARNING: $currentFiles files detected! This is too many." -ForegroundColor Red
    Write-Host "This likely includes node_modules and build files." -ForegroundColor Red
    Write-Host ""
    
    Write-Host "Step 2: Removing all files from git tracking..." -ForegroundColor Yellow
    git rm -r --cached .
    Write-Host ""
    
    Write-Host "Step 3: Adding .gitignore first..." -ForegroundColor Yellow
    git add .gitignore
    Write-Host ""
    
    Write-Host "Step 4: Adding only essential project files..." -ForegroundColor Yellow
    
    # Add Frontend source files
    if (Test-Path "DocAndSurveyManagement\Frontend\src") {
        git add "DocAndSurveyManagement/Frontend/src/"
        Write-Host "✓ Added Frontend src/" -ForegroundColor Green
    }
    
    if (Test-Path "DocAndSurveyManagement\Frontend\public") {
        git add "DocAndSurveyManagement/Frontend/public/"
        Write-Host "✓ Added Frontend public/" -ForegroundColor Green
    }
    
    if (Test-Path "DocAndSurveyManagement\Frontend\package.json") {
        git add "DocAndSurveyManagement/Frontend/package.json"
        Write-Host "✓ Added Frontend package.json" -ForegroundColor Green
    }
    
    if (Test-Path "DocAndSurveyManagement\Frontend\package-lock.json") {
        git add "DocAndSurveyManagement/Frontend/package-lock.json"
        Write-Host "✓ Added Frontend package-lock.json" -ForegroundColor Green
    }
    
    # Add Backend files if they exist
    if (Test-Path "DocAndSurveyManagement\Backend") {
        git add "DocAndSurveyManagement/Backend/"
        Write-Host "✓ Added Backend/" -ForegroundColor Green
    }
    
    # Add root files
    if (Test-Path "README.md") {
        git add "README.md"
        Write-Host "✓ Added README.md" -ForegroundColor Green
    }
    
    if (Test-Path "package.json") {
        git add "package.json"
        Write-Host "✓ Added root package.json" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "Step 5: Checking cleaned status..." -ForegroundColor Yellow
    $newCount = (git status --porcelain).Count
    Write-Host "Files to be committed after cleanup: $newCount" -ForegroundColor White
    Write-Host ""
    
    if ($newCount -lt 200) {
        Write-Host "SUCCESS: Repository cleaned! Ready to commit." -ForegroundColor Green
        Write-Host ""
        Write-Host "Next steps:" -ForegroundColor Cyan
        Write-Host "1. Review the files: git status" -ForegroundColor White
        Write-Host "2. Commit: git commit -m 'Clean repository - remove unnecessary files'" -ForegroundColor White
        Write-Host "3. Push: git push origin main" -ForegroundColor White
    } else {
        Write-Host "WARNING: Still $newCount files. You may need manual cleanup." -ForegroundColor Yellow
        Write-Host "Check: git status" -ForegroundColor White
    }
    
} else {
    Write-Host "Repository looks clean with $currentFiles files." -ForegroundColor Green
    Write-Host "You can proceed with: git add . && git commit -m 'Update project'" -ForegroundColor White
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "           Cleanup Complete" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Show current status
Write-Host ""
Write-Host "Current repository status:" -ForegroundColor Yellow
git status --short
